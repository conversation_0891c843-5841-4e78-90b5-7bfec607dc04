[General]
skip-proxy = ***********/16,10.0.0.0/8,**********/12,localhost,*.local,e.crashlynatics.com
bypass-tun = 10.0.0.0/8,**********/10,*********/8,***********/16,**********/12,*********/24,*********/24,***********/24,***********/16,**********/15,************/24,***********/24,*********/4,***************/32
dns-server = system,************,*********
allow-udp-proxy = false
host = 127.0.0.1

[Proxy]

[Remote Proxy]

[Proxy Group]

[Rule]

[Remote Rule]

[URL Rewrite]
enable = true
^https?:\/\/(www.)?(g|google)\.cn https://www.google.com 302

[Remote Rewrite]
https://raw.githubusercontent.com/Loon0x00/LoonExampleConfig/master/Rewrite/AutoRewrite_Example.list,auto

[MITM]
hostname = *.example.com,*.sample.com
enable = true
skip-server-cert-verify = true
#ca-p12 =
#ca-passphrase =
