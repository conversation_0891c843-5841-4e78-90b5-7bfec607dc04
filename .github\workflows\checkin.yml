name: Checkin
on:
  schedule:
    - cron: "45 02 * * *"
  workflow_dispatch:

jobs:
  sync_with_upstream:
    runs-on: ubuntu-latest
    name: auto checkin for traffic

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: master

      - name: Checkin
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
          architecture: "x64"
      - run: python ./.github/actions/checkin/universal.py

      - name: Timestamp
        run: date
