## 项目简介
```
📁 aggregator
├── 📁 .github
├── 📁 clash
├── 📁 cmd
├── 📁 subconverter
├── 📁 subscribe
├── 📁 tools
├── 🔧 .dockerignore
├── 🔧 .gitignore
├── 🔧 Dockerfile
├── 📄 LICENSE
├── 📄 README.md
├── 🔧 clash.ico
└── 🔧 requirements.txt
```

## 节点存储位置
```
https://gist.github.com/PangTouY00/2efa3f1165304204299a6e091eee070f
```

## V2ray订阅
```
https://gist.githubusercontent.com/PangTouY00/2efa3f1165304204299a6e091eee070f/raw/ecde221dcb6b24d11f48b0eacf5cce43abf95ff6/v2ray.txt
```

## clash订阅
```
https://gist.githubusercontent.com/PangTouY00/2efa3f1165304204299a6e091eee070f/raw/ecde221dcb6b24d11f48b0eacf5cce43abf95ff6/clash.yaml
```

## 功能
打造免费代理池，爬一切可爬节点
> 拥有灵活的插件系统，如果目标网站特殊，现有功能未能覆盖，可针对性地通过插件实现

> 欢迎 Star 及 PR。对于质量较高且普适的爬取目标，亦可在 Issues 中列出，将在评估后选择性添加

## 使用方法
> 略，自行探索。我才不会告诉你入口是 `collect.py` 和 `process.py`

## 免责申明
+ 本项目仅用作学习爬虫技术，请勿滥用，不要通过此工具做任何违法乱纪或有损国家利益之事
+ 禁止使用该项目进行任何盈利活动，对一切非法使用所产生的后果，本人概不负责
