{"log": {"disabled": false, "level": "info", "timestamp": true}, "dns": {"servers": [{"tag": "dns_proxy", "address": "tls://*******", "address_resolver": "dns_resolver"}, {"tag": "dns_direct", "address": "h3://dns.alidns.com/dns-query", "address_resolver": "dns_resolver", "detour": "DIRECT"}, {"tag": "dns_fakeip", "address": "fakeip"}, {"tag": "dns_resolver", "address": "*********", "detour": "DIRECT"}, {"tag": "block", "address": "rcode://success"}], "rules": [{"outbound": ["any"], "server": "dns_resolver"}, {"geosite": ["category-ads-all"], "server": "dns_block", "disable_cache": true}, {"geosite": ["geolocation-!cn"], "query_type": ["A", "AAAA"], "server": "dns_fakeip"}, {"geosite": ["geolocation-!cn"], "server": "dns_proxy"}], "final": "dns_direct", "independent_cache": true, "fakeip": {"enabled": true, "inet6_range": "fc00::/18", "inet4_range": "**********/15"}}, "ntp": {"enabled": true, "server": "time.apple.com", "server_port": 123, "interval": "30m", "detour": "DIRECT"}, "inbounds": [{"type": "mixed", "tag": "mixed-in", "listen": "0.0.0.0", "listen_port": 2080}, {"type": "tun", "tag": "tun-in", "inet4_address": "**********/30", "inet6_address": "fdfe:dcba:9876::1/126", "auto_route": true, "strict_route": true, "stack": "mixed", "sniff": true}], "outbounds": [], "route": {"rules": [], "auto_detect_interface": true}, "experimental": {}}